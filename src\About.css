/* About.css */

@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Albert+Sans:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.about-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  overflow-x: hidden;
}

/* NAVIGATION BAR */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  background: #011935;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 4%;
  z-index: 1000;
}

.kuberaa-logo {
  height: 60px;
}

.kuberaa-logo .logo-image {
  height: 100%;
  width: auto;
  object-fit: contain;
}

.nav-group {
  display: flex;
  gap: 40px;
}

.nav-item {
  color: #ffffff;
  text-decoration: none;
  font-family: Montserrat;
  font-weight: 600;
  font-size: 18px;
  transition: color 0.3s ease;
}

.nav-item:hover,
.nav-item.active {
  color: #c59a38;
}

/* HERO SECTION */
.hero-section {
  position: relative;
  width: 100%;
  height: 400px;
  margin-top: 80px;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 400"><defs><pattern id="buildings" x="0" y="0" width="100" height="400" patternUnits="userSpaceOnUse"><rect x="0" y="200" width="20" height="200" fill="rgba(255,255,255,0.1)"/><rect x="25" y="150" width="25" height="250" fill="rgba(255,255,255,0.08)"/><rect x="55" y="180" width="20" height="220" fill="rgba(255,255,255,0.12)"/><rect x="80" y="120" width="20" height="280" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23buildings)"/></svg>');
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(30, 58, 138, 0.3);
  z-index: 2;
}

/* ABOUT US TITLE - centered */
.hero-title {
  position: relative;
  z-index: 10;
  text-align: center;
}

.hero-title h1 {
  font-family: Montserrat;
  font-weight: 600;
  font-size: 64px;
  color: #ffffff;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
  letter-spacing: 4px;
  margin: 0;
}

/* MAIN CONTENT SECTION */
.main-content-section {
  position: relative;
  padding: 80px 0;
  background: #f8f9fa;
  min-height: 600px;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 4%;
  display: flex;
  gap: 60px;
  align-items: flex-start;
}

/* LEFT CONTENT */
.left-content {
  flex: 1;
  max-width: 50%;
}

.behind-success-title {
  font-family: Montserrat;
  font-weight: 400;
  font-size: 36px;
  letter-spacing: 6px;
  color: #c59a38;
  margin-bottom: 40px;
  text-align: left;
}

.about-description {
  font-family: Montserrat;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.6;
  color: #333333;
}

.about-description p {
  margin-bottom: 20px;
}

.about-description p:last-child {
  margin-bottom: 0;
}

.about-description strong {
  font-weight: 600;
  color: #000000;
}

/* RIGHT CONTENT */
.right-content {
  flex: 1;
  max-width: 45%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 40px;
}

/* CEO SECTION */
.ceo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  text-align: center;
}

.ceo-image {
  width: 280px;
  height: 200px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.ceo-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ceo-info {
  text-align: center;
}

.ceo-name {
  font-family: Montserrat;
  font-weight: 600;
  font-size: 28px;
  color: #c59a38;
  margin-bottom: 8px;
}

.ceo-title {
  font-family: Montserrat;
  font-weight: 500;
  font-size: 16px;
  color: #666666;
  letter-spacing: 1px;
}

/* WATERMARK IMAGE */
.watermark-image {
  position: absolute;
  top: 50px;
  right: 5%;
  width: 200px;
  height: 150px;
  opacity: 0.1;
  z-index: 1;
}

.watermark-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* MISSION SECTION */
.mission-section {
  position: relative;
  background: #011935;
  padding: 80px 4%;
  min-height: 600px;
}

.mission-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.1;
  z-index: 1;
}

.mission-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.mission-title {
  position: relative;
  font-family: Albert Sans;
  font-weight: 700;
  font-size: 48px;
  letter-spacing: 9.6px;
  color: #c59a38;
  text-align: center;
  margin-bottom: 60px;
  z-index: 10;
}

.mission-items {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 90%;
  margin: 0 auto;
  z-index: 10;
}

.mission-item {
  background: rgba(255, 255, 255, 0.55);
  border: 1px solid #797474;
  padding: 30px;
  border-radius: 8px;
}

.mission-item p {
  font-family: Albert Sans;
  font-weight: 700;
  font-size: 20px;
  letter-spacing: 2.6px;
  color: #000000;
  line-height: 1.4;
}

/* VISION SECTION */
.vision-section {
  position: relative;
  padding: 80px 4%;
  min-height: 400px;
  background: #f5f5f5;
}

.vision-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  z-index: 1;
}

.vision-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.vision-title {
  position: relative;
  font-family: Albert Sans;
  font-weight: 400;
  font-size: 48px;
  letter-spacing: 9.6px;
  color: #011935;
  text-align: center;
  margin-bottom: 40px;
  z-index: 10;
}

.vision-content {
  position: relative;
  max-width: 80%;
  margin: 0 auto;
  text-align: center;
  z-index: 10;
}

.vision-content p {
  font-family: Albert Sans;
  font-weight: 600;
  font-style: italic;
  font-size: 28px;
  letter-spacing: 2.8px;
  line-height: 1.4;
  color: #000000;
}

/* FOOTER SECTION */
.footer-section {
  background: #011935;
  padding: 60px 4% 40px;
  color: #ffffff;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-left {
  flex: 1;
}

.footer-title {
  font-family: Poppins;
  font-weight: 400;
  font-size: 48px;
  letter-spacing: 9.6px;
  color: #ffffff;
  margin-bottom: 20px;
}

.footer-logo {
  width: 120px;
  height: 80px;
  margin-bottom: 20px;
}

.footer-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.footer-subtitle {
  font-family: Poppins;
  font-weight: 500;
  font-size: 24px;
  letter-spacing: 4.8px;
  color: #ffffff;
  margin-bottom: 30px;
}

.footer-address {
  margin-bottom: 30px;
}

.footer-address p {
  font-family: Poppins;
  font-weight: 400;
  font-size: 20px;
  letter-spacing: 4px;
  color: #ffffff;
  margin-bottom: 5px;
}

.footer-contact {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.footer-email,
.footer-phone {
  font-family: Poppins;
  font-weight: 400;
  font-size: 24px;
  letter-spacing: 4.8px;
  color: #ffffff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-email:hover,
.footer-phone:hover {
  color: #c59a38;
}

.footer-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: flex-end;
}

.social-link {
  font-family: Poppins;
  font-weight: 400;
  font-size: 24px;
  letter-spacing: 4.8px;
  color: #ffffff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.social-link:hover {
  color: #c59a38;
}

/* RESPONSIVE DESIGN */
@media (max-width: 768px) {
  .nav-group {
    gap: 20px;
  }

  .nav-item {
    font-size: 16px;
  }

  .hero-title h1 {
    font-size: 48px;
    letter-spacing: 2px;
  }

  .content-container {
    flex-direction: column;
    gap: 40px;
  }

  .left-content,
  .right-content {
    max-width: 100%;
  }

  .right-content {
    padding-top: 0;
  }

  .behind-success-title {
    font-size: 28px;
    letter-spacing: 4px;
  }

  .ceo-image {
    width: 240px;
    height: 160px;
  }

  .ceo-name {
    font-size: 24px;
  }

  .footer-content {
    flex-direction: column;
    gap: 40px;
  }

  .social-links {
    align-items: flex-start;
  }
}
