
// styles.css
            
@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Albert+Sans:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
:root {
  --image-0: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABcAAAAXCAYAAADgKtSgAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADvSURBVHgB7VTRDYIwED2IA3SEjsAIOIIbuIFuIG7ABuoE4gQwAhvICDpBfZeUpDmPEGp/NL7kten1+no97iACnHMleAVbcEupwGLuHXtKAR+txJ0SIAefiv1BicQbxX6jVPB578EBrOgbkPHApSjsfZZls3nHOYPJgkY9AwerVEsZ7Fdi7+TPtFqVhX2youWwYOtnbY8vN3hFndNylBPCIQ48xIiP4PxeSC9bg+iLmLQwGjx7My4gVGPaCZ8iNvKjWJ81pyhxRN0L06C42RjxQblM7YlPPugs/uI/Js4dymW0FvawjjuxN/Urlo3VvQCDqbowqW1HRQAAAABJRU5ErkJggg==);
  --image-1: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIISURBVHgBrVb/dYIwED76/L92gqYTlBHsBDoCnaA6Qd1AnEC7gU6AnaBuAE4AG1zvzEVOXiAqfO+dJwm5n8lHImgBIo5JsRgZMtCOSuT8P4qiI9wKcjQhyUhK7IeMJAk5W+HwWGkfkXZGai6PBckPyVHkHhiSmOQL6jakVOaFzixREW3R9q8X2IbYcpjoyUwG877OaP03yYbEiNNcbGf6JYc59ECjUhsZW8ozb8LxiH5iteYYMkhqCrZHfAwKkj31ZyuvVJ5lzqY9ZmiPgYNpcRSr0viQu7WS5dy1Rkp76eMIAhBDmUTI+JXMKsnWiPzRux8q21aDs64MVWbcg5lnPsGaJHJfwMp+8qQi9wWTQH2WFhT9rvmOZPQpjwYD7PIE3XAZFV2lkkAOjTUPOXwVvYcwTqLfoYfDF9HPEEbV0A85dGfIQBhT0Sfo4dBtkolvhzo0NtcOuoDXdGQ88/pYJJ752R3HYhZkGrQsUzZYJUVL0JkaL1vWXzHNrdRmMExtccfai0OmtkLNx43nM+ic8diblJR7acASBm+qXYDOjPpfuQ+lK9kSBgZaIj9DD2aqNL2/9squ/gAf9ITu4waHu2JslN2Ex/UlKgV78WEUJGuwPSrgPvA+YHpLoO7fmvo890WV4vBIO8NDSwQH7IdSbEya9qOAcy6P6+cYOr6dUJeeNV/3vST+D0GsxLp0eZzIAAAAAElFTkSuQmCC);
  --image-2: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAXCAYAAAAP6L+eAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEeSURBVHgBrZWBEYMgDEVhA0dwBEdwhI5iJ9BOoBvUDRyBbuAIuoHdgIZrchcRIdjmLucZwoN8ImoFZq2d4FGooz201i8lMGC4+U/wN/idgo39mgEf0Rf0QgiewDfw0h8wHATPGhfrBdAWc2+hwRJX7FlswAl1BErVdkqQVON7EZMEYhXmD0pQlpFIghW6vFlJTCIJgy6Hw0rAo5JgVVsWlMHPJJlPO0AIDknShfS+At9JgrHZZnw4MTjpSZJU/9o1aTux2KGSX3a8OzD/cHOhLQH9Fgsdbi6080CG5eRJwnQdvDiBGhaTSWITd8AlSazgDrgkCTZ/8g7IksSV4rdUAp6WxO8AIZhAG5OP+r3WuHVX0gp/5FFlGFZXBYbWD7u1n7Bt48agAAAAAElFTkSuQmCC);
  --image-3: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAWCAYAAADTlvzyAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEMSURBVHgB7ZaNDYIwFIRfiQPgBMImuAlOIE6AI7CBbOAI4CSwAThBvRceCT9FkADGxEsuBdreFwr9UQRprW0UZ9ihdfSEI6VUrgSYrQirlQPoKsA83CS0jY4Wtd/spBYWZzbyHYs2Vhd4oOXVzsQ39HVbGRzBDs0U94VDuOhk+yZgU4nU2xMgNhxInyH1gHc4HWh809Uf3QV5UlcY+qSSOQj0G0PCw5oZQjKpCwcg/Oyq5ZP08k1AwxvE+r0KaWMagVb+6LTAVEphH5d7qubUo1HN1xfY5TbcdixvRxOFsBJFzK6Hi9dG+lCTgR14TjP19ZXmD/w94OYb8PZHDAE6KAJ4dFeYC6PqEFW+AM93sbRee8tpAAAAAElFTkSuQmCC);
  --image-4: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADlSURBVHgBtZbhEcIgDIWfTtAN7CiO4AZ2BDcoTuAKbqAbqBPoBnWDjoCJ5gemBfQI312OHoW84wVK4b0/+HlOFC1K8WkuKGRBSUZqm+gAAgUsKZ6oCAvcUBEWeKAmVIMmUeQzLODdMpP8arJNRcAFiR2sEZtGERhQA7WKHaxRqxjN/FcizvIzERO5ByI9rGFrAquYLayhpJ06E1WK7pRIHxnXRg5q/j6ZEZlMkr4Y+Y0idoU1GbgveJ8EvyA2DGruW8hEIGFZFvyLrOZYTUAJdX5q3RdFF3ooRs2GYk3Bzyt8fiT2L5D/Ys86HubnAAAAAElFTkSuQmCC);
  --image-5: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAAVCAYAAACQcBTNAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAChSURBVHgB7ZJRDcMwDESdqQDKYBBWCIMwCIMyBsMwBhuCrgxSBAmDhoF7UVopsdxW+e+TrMQ6x7FOJlJg5ieiR0yccDHXCt+s47SOW7hGNH4oUw0Ijwiycy+6vXL9Qvv8a4oLGnzV4WyXvBV6B329B4PE43KlY341Y3xrim302VLyMXKjcu6Rkr/BGGOLp4rP91yvsu4szpHL/6G07Cs+F2frs5A9lPv6vgAAAABJRU5ErkJggg==);
  --custom-width: 100vw;
  --design-width: 1512;
  --ratio: calc(var(--custom-width) / var(--design-width));
}

/* Default classes */
.pos-abs {
  position: absolute;
}
.fill-parent {
  width: 100%;
  height: 100%;
}
.bg-contain {
  background-size: contain;
}
.bg-cover {
  background-size: cover;
}
.bg-auto {
  background-size: auto;
}
.bg-crop {
  background-size: 100% 100%;
}
.bg-no-repeat {
  background-repeat: no-repeat;
}
.pos-init {
  top: 0px;
  left: 0px;
}
.image-div {
  background-color: transparent;
  background-position: center;
}
/* Default classes end */

body {
  margin: 0px 0px;
  padding: 0px;
}
.parent-div {
  position: relative;
  width: var(--custom-width);
  height: calc(3321 * var(--ratio));
  overflow: hidden;
  margin: auto;
  padding: 0px;
  box-sizing: border-box;
}
.about-us-5581109 {
  width: 100%;
  height: 100%;
  top: 0%;
  left: 0%;
  z-index: 1;
  transform: rotate(0deg);
  overflow: hidden;
  background: #ffffffff;
}
.nav-bar-5991098 {
  width: 100%;
  height: 4.37%;
  top: 0%;
  left: -0.13%;
  z-index: 13;
  transform: rotate(0deg);
  overflow: hidden;
  background: #011935ff;
}
.nodeBg-709926,
.nodeBg-5581116,
.nodeBg-5581120,
.nodeBg-709940 {
  background-position: center;
  background-repeat: no-repeat;
  object-fit: cover;
  opacity: 1;
}
.kuberaa-logo-2-709926 {
  width: 7.08%;
  height: 86.9%;
  top: 6.9%;
  left: 3.37%;
  z-index: 1;
  transform: rotate(0deg);
  overflow: hidden;
}
.group-1-5991099 {
  width: 37.04%;
  height: 16.55%;
  top: 41.38%;
  left: 57.74%;
  z-index: 0;
  transform: rotate(0deg);
}
.home-5991100,
.home-5581112 {
  width: 11.25%;
  height: 100%;
  top: 0%;
  left: 0%;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.home-5991100-0,
.projects-5991101-0,
.about-us-5991102-0,
.contact-us-5991103-0,
.home-5581112-0,
.projects-5581113-0,
.about-us-5581114-0,
.contact-us-5581115-0 {
  font-size: calc(20 * var(--ratio));

  line-height: calc(20 * var(--ratio));

  font-family: Montserrat;
  font-weight: 600;

  text-decoration: none;
  text-transform: none;
  white-space: pre-wrap;
  color: #ffffffff;
}
.projects-5991101,
.projects-5581113 {
  width: 15.18%;
  height: 100%;
  top: 0%;
  left: 24.64%;
  z-index: 1;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.about-us-5991102,
.about-us-5581114 {
  width: 16.79%;
  height: 100%;
  top: 0%;
  left: 51.61%;
  z-index: 2;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.contact-us-5991103,
.contact-us-5581115 {
  width: 20%;
  height: 100%;
  top: 0%;
  left: 80%;
  z-index: 3;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.nodeBg-5581110 {
  background-position: center;
  background-repeat: no-repeat;
  width: calc(1509 * var(--ratio));
  height: calc(849.2191974742262 * var(--ratio));
  left: calc(0 * var(--ratio));
  top: calc(0 * var(--ratio));
  object-fit: cover;
  opacity: 1;
}
.image-23-5581110 {
  width: 99.74%;
  height: 10.36%;
  top: 0%;
  left: 0%;
  z-index: 0;
  transform: rotate(0deg);
  overflow: hidden;
}
.image-15-5581116 {
  width: 9.39%;
  height: 5.81%;
  top: 0%;
  left: 0.86%;
  z-index: 2;
  transform: rotate(0deg);
  overflow: hidden;
}
.group-2-5581111 {
  width: 37.04%;
  height: 0.72%;
  top: 0.63%;
  left: 58.27%;
  z-index: 1;
  transform: rotate(0deg);
}
.about-us-5581117 {
  width: 17.92%;
  height: 1.78%;
  top: 4.91%;
  left: 40.67%;
  z-index: 3;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.about-us-5581117-0 {
  font-size: calc(48 * var(--ratio));

  line-height: calc(48 * var(--ratio));

  font-family: Montserrat;
  font-weight: 500;

  text-decoration: none;
  text-transform: none;
  white-space: pre-wrap;
  color: #ffffffff;
}
.behind-the-succ-5581118 {
  width: 30.42%;
  height: 3.52%;
  top: 14.39%;
  left: 10.25%;
  z-index: 4;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.behind-the-succ-5581118-0 {
  font-size: calc(48 * var(--ratio));
  letter-spacing: calc(9.6 * var(--ratio));
  line-height: calc(48 * var(--ratio));

  font-family: Montserrat;
  font-weight: 400;

  text-decoration: none;
  text-transform: none;
  white-space: pre-wrap;
  color: #c59a38ff;
}
.absolutely-here-5581119 {
  width: 43.65%;
  height: 14.81%;
  top: 19.27%;
  left: 10.25%;
  z-index: 5;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.absolutely-here-5581119-0 {
  font-size: calc(24 * var(--ratio));
  letter-spacing: calc(4.8 * var(--ratio));
  line-height: calc(24 * var(--ratio));

  font-family: Montserrat;
  font-weight: 400;

  text-decoration: none;
  text-transform: none;
  white-space: pre-wrap;
  color: #000000ff;
}
.k-kemparaj-5581121 {
  width: 14.55%;
  height: 1.32%;
  top: 19.96%;
  left: 70.11%;
  z-index: 8;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.k-kemparaj-5581121-0 {
  font-size: calc(36 * var(--ratio));

  line-height: calc(36 * var(--ratio));

  font-family: Montserrat;
  font-weight: 500;

  text-decoration: none;
  text-transform: none;
  white-space: pre-wrap;
  color: #c59a38ff;
}
.ceo-of-the-comp-5581122 {
  width: 15.87%;
  height: 0.72%;
  top: 21.71%;
  left: 69.44%;
  z-index: 9;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.ceo-of-the-comp-5581122-0 {
  font-size: calc(20 * var(--ratio));

  line-height: calc(20 * var(--ratio));

  font-family: Montserrat;
  font-weight: 500;

  text-decoration: none;
  text-transform: none;
  white-space: pre-wrap;
  color: #524b4bff;
}
.image-24-5581120 {
  width: 24.42%;
  height: 10.57%;
  top: 22.58%;
  left: 65.15%;
  z-index: 6;
  transform: rotate(0deg);
  overflow: hidden;
}
.image-2-599934 {
  width: 22.75%;
  height: 10.39%;
  top: 22.76%;
  left: 66.01%;
  z-index: 7;
  transform: rotate(0deg);
  overflow: hidden;
}
.frame-17-5581134 {
  width: 100%;
  height: 23.04%;
  top: 40.11%;
  left: 0%;
  z-index: 10;
  transform: rotate(0deg);
  overflow: hidden;
  background: #011935ff;
}
.our-mission-5581135 {
  width: 27.12%;
  height: 7.58%;
  top: 9.54%;
  left: 37.04%;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.our-mission-5581135-0 {
  font-size: calc(48 * var(--ratio));
  letter-spacing: calc(9.6 * var(--ratio));
  line-height: calc(48 * var(--ratio));

  font-family: Albert Sans;
  font-weight: 700;

  text-decoration: none;
  text-transform: none;
  white-space: pre-wrap;
  color: #c59a38ff;
}
.nodeBg-5581144 {
  background-position: center;
  background-repeat: no-repeat;
  width: calc(902.6024667126362 * var(--ratio));
  height: calc(1227.3965284653875 * var(--ratio));
  left: calc(-3.2244887449695065 * var(--ratio));
  top: calc(-76.47365115861804 * var(--ratio));
  object-fit: cover;
  opacity: 1;
}
.image-20-5581144 {
  width: 59.63%;
  height: 71.9%;
  top: 18.56%;
  left: 20.17%;
  opacity: 0.1;
  z-index: 5;
  transform: rotate(0deg);
  overflow: hidden;
}
.frame-12-5581136 {
  width: 92.59%;
  height: 12.03%;
  top: 23.14%;
  left: 3.44%;
  z-index: 1;
  transform: rotate(0deg);
  overflow: hidden;
  background: #ffffff8c;
  box-sizing: border-box;
  border-left: 1px solid #797474ff;
  border-right: 1px solid #797474ff;
  border-top: 1px solid #797474ff;
  border-bottom: 1px solid #797474ff;
}
.to-deliver-arch-5581137 {
  width: 80.86%;
  height: 26.09%;
  top: 36.96%;
  left: 4.43%;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.to-deliver-arch-5581137-0,
.to-foster-a-vib-5581139-0,
.to-prioritize-s-5581141-0,
.to-continuously-5581143-0 {
  font-size: calc(20 * var(--ratio));
  letter-spacing: calc(2.6 * var(--ratio));
  line-height: calc(20 * var(--ratio));

  font-family: Albert Sans;
  font-weight: 700;

  text-decoration: none;
  text-transform: none;
  white-space: pre-wrap;
  color: #000000ff;
}
.frame-13-5581138 {
  width: 92.59%;
  height: 12.03%;
  top: 36.6%;
  left: 3.44%;
  z-index: 2;
  transform: rotate(0deg);
  overflow: hidden;
  background: #ffffff8c;
  box-sizing: border-box;
  border-left: 1px solid #797474ff;
  border-right: 1px solid #797474ff;
  border-top: 1px solid #797474ff;
  border-bottom: 1px solid #797474ff;
}
.to-foster-a-vib-5581139 {
  width: 63.64%;
  height: 26.09%;
  top: 36.96%;
  left: 4.43%;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.frame-14-5581140 {
  width: 92.59%;
  height: 12.03%;
  top: 50.07%;
  left: 3.44%;
  z-index: 3;
  transform: rotate(0deg);
  overflow: hidden;
  background: #ffffff8c;
  box-sizing: border-box;
  border-left: 1px solid #797474ff;
  border-right: 1px solid #797474ff;
  border-top: 1px solid #797474ff;
  border-bottom: 1px solid #797474ff;
}
.to-prioritize-s-5581141 {
  width: 83.5%;
  height: 26.09%;
  top: 36.96%;
  left: 4.43%;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.frame-15-5581142 {
  width: 92.59%;
  height: 12.03%;
  top: 63.53%;
  left: 3.44%;
  z-index: 4;
  transform: rotate(0deg);
  overflow: hidden;
  background: #ffffff8c;
  box-sizing: border-box;
  border-left: 1px solid #797474ff;
  border-right: 1px solid #797474ff;
  border-top: 1px solid #797474ff;
  border-bottom: 1px solid #797474ff;
}
.to-continuously-5581143 {
  width: 86.5%;
  height: 26.09%;
  top: 36.96%;
  left: 4.43%;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.nodeBg-5581145 {
  background-position: center;
  background-repeat: no-repeat;
  object-fit: cover;
  opacity: 0.5;
}
.frame-18-5581145 {
  width: 85.19%;
  height: 11.47%;
  top: 68.02%;
  left: 6.88%;
  z-index: 11;
  transform: rotate(0deg);
  overflow: hidden;
}
.our-vision-5581146 {
  width: 30.2%;
  height: 15.22%;
  top: 17.59%;
  left: 34.86%;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.our-vision-5581146-0 {
  font-size: calc(48 * var(--ratio));
  letter-spacing: calc(9.6 * var(--ratio));
  line-height: calc(48 * var(--ratio));

  font-family: Albert Sans;
  font-weight: 400;

  text-decoration: none;
  text-transform: none;
  white-space: pre-wrap;
  color: #011935ff;
}
.to-redefine-urb-5581147 {
  width: 88.2%;
  height: 32.81%;
  top: 45.67%;
  left: 6.06%;
  z-index: 1;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.to-redefine-urb-5581147-0 {
  font-size: calc(28 * var(--ratio));
  letter-spacing: calc(2.8 * var(--ratio));
  line-height: calc(28 * var(--ratio));

  font-family: Albert Sans;
  font-weight: 600;
  font-style: italic;
  text-decoration: none;
  text-transform: none;
  white-space: pre-wrap;
  color: #000000ff;
}
.frame-8-599910 {
  width: 100%;
  height: 15.63%;
  top: 84.37%;
  left: 0%;
  z-index: 12;
  transform: rotate(0deg);
  overflow: hidden;
  background: #011935ff;
}
.ku-kuberaa-599911 {
  width: 24.47%;
  height: 13.87%;
  top: 6.94%;
  left: 4.43%;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.ku-kuberaa-599911-0 {
  font-size: calc(48 * var(--ratio));
  letter-spacing: calc(9.6 * var(--ratio));
  line-height: calc(48 * var(--ratio));

  font-family: Poppins;
  font-weight: 400;

  text-decoration: none;
  text-transform: none;
  white-space: pre-wrap;
  color: #ffffffff;
}
.kuberaa-logo-2-709940 {
  width: 9.85%;
  height: 33.53%;
  top: 11.56%;
  left: 80.22%;
  z-index: 15;
  transform: rotate(0deg);
  overflow: hidden;
}
.licensed-in-ben-599912 {
  width: 12.43%;
  height: 13.87%;
  top: 25.24%;
  left: 4.43%;
  z-index: 1;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.licensed-in-ben-599912-0 {
  font-size: calc(24 * var(--ratio));
  letter-spacing: calc(4.8 * var(--ratio));
  line-height: calc(24 * var(--ratio));

  font-family: Poppins;
  font-weight: 500;

  text-decoration: none;
  text-transform: none;
  white-space: pre-wrap;
  color: #ffffffff;
}
.no-99kemparaju--599913 {
  width: 22.22%;
  height: 23.12%;
  top: 50.67%;
  left: 4.23%;
  z-index: 2;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.no-99kemparaju--599913-0 {
  font-size: calc(20 * var(--ratio));
  letter-spacing: calc(4 * var(--ratio));
  line-height: calc(20 * var(--ratio));

  font-family: Poppins;
  font-weight: 400;

  text-decoration: none;
  text-transform: none;
  white-space: pre-wrap;
  color: #ffffffff;
}
.krbuildtechs-599917 {
  width: 17%;
  height: 3.08%;
  top: 55.3%;
  left: 78.77%;
  z-index: 6;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.krbuildtechs-599917-0,
.krbuildtechs-599916-0,
.krbuildtechs-599918-0,
.krbuildtechsgma-599915-0,
.c-91-9845491599-599914-0,
.krbuildtechs-599919-0 {
  font-size: calc(24 * var(--ratio));
  letter-spacing: calc(4.8 * var(--ratio));
  line-height: calc(24 * var(--ratio));

  font-family: Poppins;
  font-weight: 400;

  text-decoration: none;
  text-transform: none;
  white-space: pre-wrap;
  color: #ffffffff;
}
.frame-599929 {
  width: 1.76%;
  height: 5.14%;
  top: 55.88%;
  left: 74.93%;
  z-index: 13;
  transform: rotate(0deg);
  overflow: hidden;
}
.nodeBg-599930 {
  opacity: 1;
  background-image: var(--image-0);
}
.vector-599930,
.vector-599922 {
  width: 83.33%;
  height: 83.33%;
  top: 8.33%;
  left: 8.33%;
  z-index: 0;
  transform: rotate(0deg);
  overflow: hidden;
}
.frame-599921 {
  width: 2.12%;
  height: 6.17%;
  top: 64.74%;
  left: 74.74%;
  z-index: 9;
  transform: rotate(0deg);
  overflow: hidden;
}
.nodeBg-599922 {
  opacity: 1;
  background-image: var(--image-1);
}
.krbuildtechs-599916 {
  width: 17%;
  height: 3.08%;
  top: 64.74%;
  left: 78.77%;
  z-index: 5;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.krbuildtechs-599918 {
  width: 17%;
  height: 6.94%;
  top: 74.57%;
  left: 78.77%;
  z-index: 7;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.frame-599927 {
  width: 1.76%;
  height: 5.14%;
  top: 75.53%;
  left: 74.93%;
  z-index: 12;
  transform: rotate(0deg);
  overflow: hidden;
}
.nodeBg-599928 {
  opacity: 1;
  background-image: var(--image-2);
}
.vector-599928 {
  width: 79.17%;
  height: 83.33%;
  top: 8.33%;
  left: 10.42%;
  z-index: 0;
  transform: rotate(0deg);
  overflow: hidden;
}
.krbuildtechsgma-599915 {
  width: 27.05%;
  height: 6.36%;
  top: 84.97%;
  left: 8.27%;
  z-index: 4;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.frame-599923 {
  width: 2.12%;
  height: 6.17%;
  top: 85.16%;
  left: 4.23%;
  z-index: 10;
  transform: rotate(0deg);
  overflow: hidden;
}
.nodeBg-599924 {
  opacity: 1;
  background-image: var(--image-3);
}
.vector-599924 {
  width: 83.33%;
  height: 66.67%;
  top: 16.67%;
  left: 8.33%;
  z-index: 0;
  transform: rotate(0deg);
  overflow: hidden;
}
.c-91-9845491599-599914 {
  width: 17%;
  height: 6.36%;
  top: 85.16%;
  left: 41.07%;
  z-index: 3;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.frame-599925 {
  width: 2.12%;
  height: 6.17%;
  top: 85.36%;
  left: 37.04%;
  z-index: 11;
  transform: rotate(0deg);
  overflow: hidden;
}
.nodeBg-599926 {
  opacity: 1;
  background-image: var(--image-4);
}
.vector-599926 {
  width: 75%;
  height: 75%;
  top: 12.5%;
  left: 12.5%;
  z-index: 0;
  transform: rotate(0deg);
  overflow: hidden;
}
.krbuildtechs-599919 {
  width: 17%;
  height: 3.08%;
  top: 85.36%;
  left: 78.77%;
  z-index: 8;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}
.frame-599931 {
  width: 1.62%;
  height: 4.72%;
  top: 86.13%;
  left: 74.93%;
  z-index: 14;
  transform: rotate(0deg);
  overflow: hidden;
}
.nodeBg-599932 {
  opacity: 1;
  background-image: var(--image-5);
}
.vector-599932 {
  width: 43.75%;
  height: 83.33%;
  top: 8.33%;
  left: 29.17%;
  z-index: 0;
  transform: rotate(0deg);
  overflow: hidden;
}


// Generated with Dualite Figma Plugin



            