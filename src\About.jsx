// About.jsx

import React from "react";
import { Link } from "react-router-dom";
import "./About.css";

const About = () => {
  return (
    <div className="about-page">
      {/* NAVIGATION BAR */}
      <header className="nav-bar">
        <div className="kuberaa-logo">
          <img
            src="https://s3-alpha-sig.figma.com/img/947c/518a/a96e128dd3a5444381161e74e580d564?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=G7pL8R12ijoiHJPKjGj1Ybj18u~OV27YXncaNcp9G5dXnqfKhJPgVC-bCaSSCrX6RAZ62cX8HNKs9h~jod8MD~1hd3WXCo3zYBvujzwFr5zxxQtE6cWJTILbw-mPsQzZcY2brXZ~JEMp6~vJEKIEw98AqnWGdKBkqVZKaZ3kiN9aUwL1io83vEElk84tVBBNAesjTAKI~q1ZeNB52kabLxCVy2pJ7OzJSPm-Q2XWdeAS8AVSdukdUzXgiX1K-scwggFUIPy0vfonyO6~1w5S9XRpw-ga7das0dbnrDAqTwp0b4XUFvbZA6eF97lG6BqySR7y7Wg~q6kf5NJu1~NTOQ__"
            className="logo-image"
            alt="Kuberaa Logo"
          />
        </div>

        <nav className="nav-group">
          <Link to="/" className="nav-item">
            Home
          </Link>
          <Link to="#projects" className="nav-item">
            Projects
          </Link>
          <Link to="/about" className="nav-item active">
            About us
          </Link>
          <Link to="#contact" className="nav-item">
            Contact us
          </Link>
        </nav>
      </header>

      {/* HERO SECTION WITH BLUE BUILDING BACKGROUND */}
      <section className="hero-section">
        <div className="hero-background">
          {/* Blue building background image */}
          <div className="hero-overlay"></div>
        </div>

        {/* ABOUT US TITLE - centered */}
        <div className="hero-title">
          <h1>ABOUT US</h1>
        </div>
      </section>

      {/* MAIN CONTENT SECTION */}
      <section className="main-content-section">
        <div className="content-container">
          {/* Left side content */}
          <div className="left-content">
            <h2 className="behind-success-title">BEHIND THE SUCCESS</h2>

            <div className="about-description">
              <p>
                Absolutely! Here's a shorter and more concise version of the
                About Us page for a real estate business:
              </p>
              <p>
                <strong>About Us</strong>
              </p>
              <p>
                At [Your Company Name], we make real estate simple, personal,
                and rewarding. Whether you're buying, selling, or investing, our
                experienced team is here to guide you every step of the way.
              </p>
              <p>
                We specialize in residential and commercial properties, offering
                expert advice, local market knowledge, and a commitment to
                client satisfaction.
              </p>
              <p>
                With a passion for property and a focus on results, we're not
                just helping you find a space—we're helping you find your
                future.
              </p>
            </div>
          </div>

          {/* Right side - CEO section */}
          <div className="right-content">
            <div className="ceo-section">
              <div className="ceo-image">
                <img
                  src="https://s3-alpha-sig.figma.com/img/1691/2d28/88121cd5f99107210bdde8c486177a78?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=Ni3vUxBUubA1WzgTwdP6Tl-hsR2HznNQqsXmJwOxF0khrdDMFJogkWyogNXsak-bmQHs2LIzxxHeuDZQXDBgTIfTlGu7q2bExiBMLkwO6VMwIbQPm9bXinxS9wEhMBZ2QhJahCOdOOi5vFaVS07owQUrPCTrHQUVPOfQ~c-jEU9sodMiw3xqflxFByDxJ31DK7yF7NUo8GmsqZ-eB~dxwBUKithaXLxlx7x0ckhQWOmdsnFA4iZ5Ic5TYi-cLNSW8VSxF~68451QmfMkDVJEAngHbnYGBGqcoAt5quPUIGIjdkeMkSaxa0WyLH9tigLmwyn6hoWGb~Szq9uGpQ8EKw__"
                  alt="K. Kemparaj"
                />
              </div>
              <div className="ceo-info">
                <h3 className="ceo-name">K. Kemparaj</h3>
                <p className="ceo-title">CEO OF THE COMPANY</p>
              </div>
            </div>
          </div>
        </div>

        {/* WATERMARK IMAGE */}
        <div className="watermark-image">
          <img
            src="https://firebasestorage.googleapis.com/v0/b/figma-plugin-a7287.appspot.com/o/user-images%2F24-may-2025%2Fpritam1748067673257%2Fimage-599-934.png?alt=media&token=0"
            alt="Kuberaa Watermark"
          />
        </div>
      </section>

      {/* OUR MISSION SECTION */}
      <section className="mission-section">
        <div className="mission-bg">
          <img
            src="https://s3-alpha-sig.figma.com/img/c6b2/dac1/83381d611a7ef2d08b62e58e2e26c916?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=uSAOVnEb8fD3YaB5IUsI2AzJzrOWQyloBsWVSL7PghbK6nrIq4JJWInKpjg9Eu0ipO5o-EZuBU0pzktROnXIfiwHu0yiezkj6-zFkpLHvzJSLDNeNZ28d3uWBF89a7LvLR7Ppnjf5gMb3d2qAkwT6GsVc1f-cAfTDt5~iV1gjk1LmVm~o9ZxtX8l6zbxLzovTb5CCtvgZbZWBdUgNG1WOyrSZbiG~WWSbxH6Yp4UWgtFlcjwf0XjG3S6qcDo9RVMEph3wvAkGRJKlrVQpDOCL8ryk5lPRWxYpNBj4sTFzyv9ujeV3qukOTBgTF0S8rYnn0VfIjR7yqTJ4BYe~-lgaA__"
            alt="Mission Background"
            className="mission-bg-image"
          />
        </div>

        <h2 className="mission-title">OUR MISSION</h2>

        <div className="mission-items">
          <div className="mission-item">
            <p>
              To deliver architecturally distinctive villas that combine
              elegance with thoughtful functionality.
            </p>
          </div>

          <div className="mission-item">
            <p>
              To foster a vibrant, secure, and green community in the heart of
              Bangalore.
            </p>
          </div>

          <div className="mission-item">
            <p>
              To prioritize sustainable practices, premium quality, and
              long-lasting value in every villa we build.
            </p>
          </div>

          <div className="mission-item">
            <p>
              To continuously innovate in design and amenities, keeping
              homeowners at the heart of every decision.
            </p>
          </div>
        </div>
      </section>

      {/* OUR VISION SECTION */}
      <section className="vision-section">
        <div className="vision-bg">
          <img
            src="https://s3-alpha-sig.figma.com/img/484a/e880/145eee6b9ee948e607112f13e92fd35b?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=sFC9aiOVoIMztq4ZEnHcbzD5s2hwSoIz-k8TMypzWE5XUaS9pe8pit-z5JfDv-8atVwkQi2EK~yJLuQP73KQOWoV0qUQhfp6uVUL6j58mLOzXX9VQIGpLaB0QbUwwvhZq92deSKcjeOYU~3R0DSdortP834ib1fxPUQU6Ki5~9hmIS-2g-AI-lNWTDtNCvR7tsv35jzTpVJsTIT-N8OPFq5JIZpCu58CeIPKtPjZULTHLvlfMVgd7dtoWlE-kdwjvkA56i6hFPqu6KzU22m2SqeyXjrdSnBWC6khSsCuYHkwK8L~fhQWW1kdEH11S~bmZ-bjoJidkFkyRwNH~WwBHA__"
            alt="Vision Background"
            className="vision-bg-image"
          />
        </div>

        <h2 className="vision-title">OUR VISION</h2>

        <div className="vision-content">
          <p>
            To redefine urban living by creating timeless villas that embody
            luxury, comfort, and connection with nature—where every family finds
            not just a house, but a place to call home.
          </p>
        </div>
      </section>

      {/* FOOTER SECTION */}
      <footer className="footer-section">
        <div className="footer-content">
          <div className="footer-left">
            <h3 className="footer-title">KU KUBERAA</h3>
            <div className="footer-logo">
              <img
                src="https://s3-alpha-sig.figma.com/img/947c/518a/a96e128dd3a5444381161e74e580d564?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=G7pL8R12ijoiHJPKjGj1Ybj18u~OV27YXncaNcp9G5dXnqfKhJPgVC-bCaSSCrX6RAZ62cX8HNKs9h~jod8MD~1hd3WXCo3zYBvujzwFr5zxxQtE6cWJTILbw-mPsQzZcY2brXZ~JEMp6~vJEKIEw98AqnWGdKBkqVZKaZ3kiN9aUwL1io83vEElk84tVBBNAesjTAKI~q1ZeNB52kabLxCVy2pJ7OzJSPm-Q2XWdeAS8AVSdukdUzXgiX1K-scwggFUIPy0vfonyO6~1w5S9XRpw-ga7das0dbnrDAqTwp0b4XUFvbZA6eF97lG6BqySR7y7Wg~q6kf5NJu1~NTOQ__"
                alt="Kuberaa Logo"
              />
            </div>

            <p className="footer-subtitle">
              LICENSED IN
              <br />
              BENGALURU
            </p>

            <div className="footer-address">
              <p>No. 99,Kemparaju Layout</p>
              <p>Hirandahalli Village</p>
              <p>Vigro Nagar Post</p>
              <p>Bangalore- 560049</p>
            </div>

            <div className="footer-contact">
              <a href="mailto:<EMAIL>" className="footer-email">
                <EMAIL>
              </a>
              <a href="tel:+91**********" className="footer-phone">
                +91 **********
              </a>
            </div>
          </div>

          <div className="footer-right">
            <div className="social-links">
              <a href="#" className="social-link">
                kr_build_techs
              </a>
              <a href="#" className="social-link">
                kr_build_techs
              </a>
              <a href="#" className="social-link">
                kr_build_techs
              </a>
              <a href="#" className="social-link">
                kr_build_techs
              </a>
              <a href="#" className="social-link">
                kr_build_techs
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default About;
