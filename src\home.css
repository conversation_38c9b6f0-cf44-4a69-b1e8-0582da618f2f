/* home.css */

@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Albert+Sans:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

:root {
  --primary-color: #0047a0;
  --primary-dark: #001a3a;
  --text-light: #ffffff;
  --text-dark: #000000;
  --accent-color: #c59a38;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Montserrat", sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
}

.modern-layout {
  width: 100%;
  overflow-x: hidden;
}

/* HEADER STYLES */
.main-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 5%;
  background: linear-gradient(
    90deg,
    var(--primary-color) 0%,
    var(--primary-dark) 100%
  );
  position: fixed;
  width: 100%;
  z-index: 1000;
}

.logo-container {
  width: 80px;
}

.logo-image {
  width: 100%;
  height: auto;
  object-fit: contain;
}

.main-nav {
  display: flex;
  align-items: center;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-item {
  color: var(--text-light);
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: transform 0.3s ease;
  position: relative;
}

.nav-item:hover {
  transform: translateY(-2px);
}

.nav-item.active::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--text-light);
}

/* HERO SECTION */
.hero-section {
  height: 100vh;
  width: 100vw;
  background-image: url("https://s3-alpha-sig.figma.com/img/db08/0a63/a34993a96af34a6168582de75ddd5e40?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=LRAAiKXmR0VUFLWYgRYBsNkOHRIY6KBsCYQYeNA9yiVc-eefVH8Wv~HraGSPBROY55dcDjRpPnlYjyVMYVTf2iTSoTsSDewT8PKqkc-~Q4kisZMQYtm65LLQ1-a68ECJTTJCD64llJurxnrYuLzQOkn-PhDt5vcT1BDaVCfN0aXvmLMLiZQxQfwEZKjI7RbWA85lsExNtMFz3kk~G2uPp0UvPN6sechoWWBFsRynXR-DwFjEDT0x1L4sd~1sWnKGAMYHp6w~xH2Vrn1xGAQ7JlvrNzhNIDGtE9sGbZfYSQRAWmmzaw26UKBOlO9ky8WVCBr1G38oCcW5wFQbImCKtw__");
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  max-width: 100%;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  color: var(--text-light);
  max-width: 800px;
  padding: 0 2rem;
}

.location-badge {
  font-size: 1rem;
  font-weight: 300;
  margin-bottom: 1rem;
  letter-spacing: 1px;
}

.hero-title {
  font-family: "Albert Sans", sans-serif;
  font-size: 5rem;
  font-weight: 400;
  letter-spacing: 8px;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
}

.hero-tagline {
  font-size: 1.5rem;
  margin-bottom: 2.5rem;
  font-weight: 400;
}

.explore-button {
  display: inline-block;
  padding: 0.8rem 2.5rem;
  border: 2px solid var(--text-light);
  color: var(--text-light);
  text-decoration: none;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.explore-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

/* ABOUT SECTION */
.about-section {
  padding: 5rem 0;
  background-color: var(--text-light);
  position: relative;
  overflow: visible;
  width: 100%;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 2;
}

.kuberaa-watermark {
  position: absolute;
  top: 50px;
  left: 0;
  width: auto;
  font-family: "Albert Sans", sans-serif;
  font-size: 12rem;
  font-weight: 700;
  text-align: left;
  color: rgba(0, 0, 0, 0.03);
  letter-spacing: 15px;
  z-index: 1;
  text-transform: uppercase;
  pointer-events: none;
  line-height: 0.9;
  margin-left: 5%;
  max-width: 50%;
}

.service-tagline {
  text-align: center;
  font-family: "Montserrat", sans-serif;
  font-size: 1.2rem;
  font-weight: 500;
  margin-top: 3rem;
  margin-bottom: 1.5rem;
  color: var(--text-dark);
  letter-spacing: 1px;
}

.gold-divider {
  width: 100px;
  height: 2px;
  background-color: var(--accent-color);
  margin: 0 auto 2.5rem;
}

.team-title {
  font-family: "Albert Sans", sans-serif;
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 2.5rem;
  letter-spacing: 3px;
  text-transform: uppercase;
  color: var(--text-dark);
}

.about-description {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 2rem;
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--text-dark);
}

.more-about-btn {
  display: block;
  width: fit-content;
  margin: 2rem auto;
  color: var(--accent-color);
  text-decoration: underline;
  font-weight: 600;
  font-size: 1rem;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.more-about-btn:hover {
  transform: translateY(-3px);
}

.mastery-tagline {
  text-align: center;
  font-family: "Albert Sans", sans-serif;
  font-size: 1.2rem;
  font-weight: 400;
  margin: 5rem 0 1rem;
  letter-spacing: 2px;
  color: var(--text-dark);
  text-transform: uppercase;
}

.projects-heading {
  font-family: "Albert Sans", sans-serif;
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 2rem;
  letter-spacing: 3px;
  text-transform: uppercase;
  color: var(--text-dark);
}

/* PROJECTS SECTION */
.projects-section {
  padding: 5rem 0;
  background-color: #0a2a5e;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.location-card {
  position: relative;
  overflow: hidden;
  height: 300px;
  transition: transform 0.3s ease;
}

.location-card:hover {
  transform: translateY(-10px);
}

.location-image {
  height: 100%;
  width: 100%;
  position: relative;
}

.location-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.location-card:hover .location-image img {
  transform: scale(1.05);
}

.location-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
}

.location-overlay h3 {
  color: var(--text-light);
  font-family: "Albert Sans", sans-serif;
  font-size: 1.8rem;
  font-weight: 600;
  letter-spacing: 2px;
  text-transform: uppercase;
}

/* MEET THE TEAM SECTION */
.meet-team-section {
  padding: 5rem 0;
  background-color: var(--primary-color);
  color: var(--text-light);
}

.team-content {
  display: flex;
  align-items: center;
  gap: 4rem;
}

.team-logo {
  flex: 1;
  display: flex;
  justify-content: center;
}

.team-logo-img {
  width: 100%;
  max-width: 300px;
  height: auto;
}

.team-info {
  flex: 1.5;
}

.team-section-title {
  font-family: "Albert Sans", sans-serif;
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  letter-spacing: 3px;
}

.team-description p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 2rem;
}

.learn-more-btn {
  display: inline-block;
  padding: 0.8rem 2.5rem;
  border: 2px solid var(--accent-color);
  background-color: var(--accent-color);
  color: var(--text-light);
  text-decoration: none;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.learn-more-btn:hover {
  background-color: transparent;
  color: var(--accent-color);
}

/* CLIENT STORIES SECTION */
.client-stories-section {
  padding: 5rem 0;
  background-color: #ffffff;
}

.client-stories-title {
  font-family: "Albert Sans", sans-serif;
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 4rem;
  letter-spacing: 3px;
  text-transform: uppercase;
  color: var(--text-dark);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
  margin-bottom: 3rem;
}

.testimonial-card {
  text-align: center;
  padding: 1rem;
  position: relative;
}

.quote-icon {
  font-size: 4rem;
  color: #f0f0f0;
  line-height: 1;
  margin-bottom: 1rem;
  font-family: serif;
}

.testimonial-text {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-style: italic;
  color: #333;
}

.testimonial-location {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  margin-top: 0.5rem;
  color: var(--primary-color);
  font-style: normal;
}

.testimonial-author {
  font-weight: 600;
  color: #555;
}

.read-more-btn {
  display: block;
  width: fit-content;
  margin: 0 auto;
  padding: 0.8rem 2.5rem;
  border: 1px solid #333;
  color: #333;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.read-more-btn:hover {
  background-color: #333;
  color: #fff;
}

/* FEATURED PROPERTIES SECTION */
.featured-properties-section {
  padding: 5rem 0;
  background-color: #0a2a5e;
  color: var(--text-light);
}

.featured-properties-title {
  font-family: "Albert Sans", sans-serif;
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 4rem;
  letter-spacing: 3px;
  text-transform: uppercase;
  color: var(--text-light);
}

.properties-grid {
  display: flex;
  justify-content: center;
  gap: 5rem;
  margin-bottom: 3rem;
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.property-card {
  width: 450px;
  background-color: white;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.property-card:hover {
  transform: translateY(-5px);
}

.property-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.property-info {
  padding: 1.5rem;
  text-align: center;
  color: #333;
}

.property-name {
  font-family: "Albert Sans", sans-serif;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.property-location {
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  color: #555;
}

.property-description {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.property-completion {
  font-size: 0.85rem;
  margin-bottom: 1.2rem;
  color: #555;
}

.view-details-btn {
  display: inline-block;
  padding: 0.5rem 1.5rem;
  background-color: var(--accent-color);
  color: var(--text-light);
  text-decoration: none;
  font-size: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  border: none;
}

.view-details-btn:hover {
  background-color: #b38a28;
}

.view-all-properties-btn {
  display: block;
  width: fit-content;
  margin: 2rem auto 0;
  padding: 0.7rem 2.5rem;
  border: 1px solid var(--accent-color);
  color: var(--accent-color);
  text-decoration: none;
  font-size: 0.8rem;
  font-weight: 600;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  background-color: transparent;
}

/* WORK WITH US SECTION */
.work-with-us-section {
  padding: 5rem 0;
  background-color: #fff;
  text-align: center;
}

.work-with-us-title {
  font-family: "Albert Sans", sans-serif;
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 1rem;
  letter-spacing: 3px;
  text-transform: uppercase;
  color: #333;
}

.title-underline {
  width: 100px;
  height: 3px;
  background-color: var(--accent-color);
  margin: 0 auto 3rem;
}

.work-with-us-text {
  max-width: 800px;
  margin: 0 auto 3rem;
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
}

.contact-us-btn {
  display: inline-block;
  padding: 0.8rem 2.5rem;
  background-color: var(--accent-color);
  color: var(--text-light);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  border: none;
}

.contact-us-btn:hover {
  background-color: #b38a28;
}

/* FOOTER SECTION */
.footer-section {
  background-color: var(--primary-color);
  color: var(--text-light);
  padding: 4rem 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
}

.footer-left {
  flex: 1;
  max-width: 400px;
}

.footer-title {
  font-family: "Albert Sans", sans-serif;
  font-size: 2rem;
  letter-spacing: 3px;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.footer-subtitle {
  font-size: 0.9rem;
  letter-spacing: 1px;
  margin-bottom: 2rem;
  line-height: 1.4;
}

.footer-address {
  margin-bottom: 2rem;
}

.footer-address p {
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.footer-contact {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-email,
.footer-phone {
  display: flex;
  align-items: center;
  color: var(--text-light);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-email:hover,
.footer-phone:hover {
  color: var(--accent-color);
}

.email-icon,
.phone-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  background-size: contain;
  background-repeat: no-repeat;
}

.footer-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.footer-logo {
  width: 120px;
  height: auto;
  margin-bottom: 2rem;
}

.social-links {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.8rem;
}

.social-link {
  color: var(--text-light);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 30px;
}

.social-link:hover {
  color: var(--accent-color);
}

.social-link::before {
  content: "";
  position: absolute;
  left: 0;
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
}

.linkedin::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14m-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z"/></svg>');
}

.instagram::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M7.8 2h8.4C19.4 2 22 4.6 22 7.8v8.4a5.8 5.8 0 0 1-5.8 5.8H7.8C4.6 22 2 19.4 2 16.2V7.8A5.8 5.8 0 0 1 7.8 2m-.2 2A3.6 3.6 0 0 0 4 7.6v8.8C4 18.39 5.61 20 7.6 20h8.8a3.6 3.6 0 0 0 3.6-3.6V7.6C20 5.61 18.39 4 16.4 4H7.6m9.65 1.5a1.25 1.25 0 0 1 1.25 1.25A1.25 1.25 0 0 1 17.25 8 1.25 1.25 0 0 1 16 6.75a1.25 1.25 0 0 1 1.25-1.25M12 7a5 5 0 0 1 5 5 5 5 0 0 1-5 5 5 5 0 0 1-5-5 5 5 0 0 1 5-5m0 2a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3z"/></svg>');
}

.twitter::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/></svg>');
}

.facebook::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M12 2.04C6.5 2.04 2 6.53 2 12.06C2 17.06 5.66 21.21 10.44 21.96V14.96H7.9V12.06H10.44V9.85C10.44 7.34 11.93 5.96 14.22 5.96C15.31 5.96 16.45 6.15 16.45 6.15V8.62H15.19C13.95 8.62 13.56 9.39 13.56 10.18V12.06H16.34L15.89 14.96H13.56V21.96A10 10 0 0 0 22 12.06C22 6.53 17.5 2.04 12 2.04Z"/></svg>');
}

.email-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/></svg>');
}

.phone-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/></svg>');
}

/* RESPONSIVE STYLES */
@media (max-width: 768px) {
  .main-header {
    padding: 1rem;
    flex-direction: column;
  }

  .nav-list {
    margin-top: 1rem;
    gap: 1rem;
  }

  .hero-title {
    font-size: 3rem;
  }

  .hero-tagline {
    font-size: 1.2rem;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: 3rem;
  }

  .footer-left,
  .footer-right {
    width: 100%;
    max-width: 100%;
    align-items: center;
  }

  .footer-address,
  .footer-contact {
    text-align: center;
  }

  .footer-contact {
    align-items: center;
  }

  .social-links {
    align-items: center;
  }

  .social-link {
    justify-content: center;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .team-content {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
  }

  .team-logo {
    margin-bottom: 1rem;
  }

  .team-section-title {
    font-size: 2rem;
  }

  .kuberaa-watermark {
    font-size: 8rem;
    top: 20px;
  }

  .properties-grid {
    flex-direction: column;
    align-items: center;
  }

  .property-card {
    width: 90%;
    max-width: 450px;
  }

  .property-image {
    height: 250px;
  }

  .featured-properties-title {
    font-size: 2rem;
  }

  .work-with-us-title {
    font-size: 2rem;
  }

  .work-with-us-text {
    font-size: 1rem;
    padding: 0 1rem;
  }
}
